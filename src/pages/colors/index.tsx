import type { JSX } from "react";

export function Colors(): JSX.Element {
  return (
    <div className="p-8">
      <div className="space-y-6">
        <h1 className="mb-8 text-center font-bold text-4xl">Color Test Page</h1>

        <section>
          <h2 className="mb-4 font-semibold text-2xl text-gray-800">Standard Tailwind Colors</h2>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div className="rounded-lg bg-red-100 p-4">
              <h3 className="font-semibold text-red-800">Red</h3>
              <p className="text-red-600">text-red-600</p>
              <p className="text-red-500">text-red-500</p>
              <p className="text-red-400">text-red-400</p>
            </div>
            <div className="rounded-lg bg-blue-100 p-4">
              <h3 className="font-semibold text-blue-800">Blue</h3>
              <p className="text-blue-600">text-blue-600</p>
              <p className="text-blue-500">text-blue-500</p>
              <p className="text-blue-400">text-blue-400</p>
            </div>
            <div className="rounded-lg bg-green-100 p-4">
              <h3 className="font-semibold text-green-800">Green</h3>
              <p className="text-green-600">text-green-600</p>
              <p className="text-green-500">text-green-500</p>
              <p className="text-green-400">text-green-400</p>
            </div>
            <div className="rounded-lg bg-purple-100 p-4">
              <h3 className="font-semibold text-purple-800">Purple</h3>
              <p className="text-purple-600">text-purple-600</p>
              <p className="text-purple-500">text-purple-500</p>
              <p className="text-purple-400">text-purple-400</p>
            </div>
          </div>
        </section>

        <section>
          <h2 className="mb-4 font-semibold text-2xl text-gray-800">DaisyUI Semantic Colors</h2>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-primary">Primary</h3>
              <p className="text-primary">text-primary</p>
              <p className="text-primary-content">text-primary-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-secondary">Secondary</h3>
              <p className="text-secondary">text-secondary</p>
              <p className="text-secondary-content">text-secondary-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-accent">Accent</h3>
              <p className="text-accent">text-accent</p>
              <p className="text-accent-content">text-accent-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-info">Info</h3>
              <p className="text-info">text-info</p>
              <p className="text-info-content">text-info-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-success">Success</h3>
              <p className="text-success">text-success</p>
              <p className="text-success-content">text-success-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-warning">Warning</h3>
              <p className="text-warning">text-warning</p>
              <p className="text-warning-content">text-warning-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-error">Error</h3>
              <p className="text-error">text-error</p>
              <p className="text-error-content">text-error-content</p>
            </div>
            <div className="rounded-lg bg-base-200 p-4">
              <h3 className="font-semibold text-neutral">Neutral</h3>
              <p className="text-neutral">text-neutral</p>
              <p className="text-neutral-content">text-neutral-content</p>
            </div>
            <div className="rounded-lg border border-base-300 bg-base-100 p-4">
              <h3 className="font-semibold text-neutral">base-100</h3>
            </div>
            <div className="rounded-lg border border-base-300 bg-base-200 p-4">
              <h3 className="font-semibold text-neutral">base-200</h3>
            </div>
            <div className="rounded-lg border border-base-300 bg-base-300 p-4">
              <h3 className="font-semibold text-neutral">base-300</h3>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
