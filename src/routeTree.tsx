import { createRootRoute, createRoute, createRouter, Outlet } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/router-devtools";

import "i18n";
import { Footer } from "@components/layout/_footer";
import { Navigation } from "@components/layout/_navigation";
import { ROUTE_PATH } from "@enums/route-path";
import { About } from "@pages/about";
import { Colors } from "@pages/colors";
import { Index } from "@pages/index";
import type { ReactElement } from "react";

const RootComponent = (): ReactElement => (
  <div className="flex h-screen flex-col">
    <Navigation />
    <div className="h-full flex-1 overflow-auto">
      <Outlet />
    </div>
    <Footer />
    <TanStackRouterDevtools />
  </div>
);

const rootRoute: ReturnType<typeof createRootRoute> = createRootRoute({ component: RootComponent });

const routes = [
  { component: Index, path: ROUTE_PATH.HOME },
  { component: About, path: ROUTE_PATH.ABOUT },
  { component: Colors, path: ROUTE_PATH.COLORS },
].map(({ path, component }) => createRoute({ component, getParentRoute: () => rootRoute, path }));

const routeTree = rootRoute.addChildren(routes);

export const router = createRouter({ routeTree });

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}
