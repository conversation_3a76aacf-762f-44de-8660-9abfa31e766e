// components/layout/Navigation.tsx

import { LanguageToggleButton } from "@components/languageToggleButton";
import { ThemeToggle } from "@components/themeToggle";
import { ROUTE_PATH, type RoutePath } from "@enums/route-path";
import { Link } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import type { JSX } from "react";
import { useTranslation } from "react-i18next";

type NavItem = {
  path: RoutePath;
  translationKey: string;
  activeClass: string;
};

export function Navigation(): JSX.Element {
  const { t } = useTranslation();

  const navItems: NavItem[] = [
    {
      activeClass: "[&.active]:text-primary",
      path: ROUTE_PATH.HOME,
      translationKey: "navigation.home",
    },
    {
      activeClass: "[&.active]:text-secondary",
      path: ROUTE_PATH.ABOUT,
      translationKey: "navigation.about",
    },
    {
      activeClass: "[&.active]:text-info",
      path: ROUTE_PATH.COLORS,
      translationKey: "navigation.colors",
    },
  ];

  return (
    <div className="navbar flex justify-between bg-base-100 px-4 py-2 shadow-sm">
      <div className="flex items-center gap-4">
        {navItems.map((item: NavItem) => (
          <Link
            key={item.path}
            to={item.path as string}
            className={cn("text-xl [&.active]:font-bold", item.activeClass)}
          >
            {t(item.translationKey)}
          </Link>
        ))}
      </div>
      <div className="flex items-center gap-4">
        <LanguageToggleButton />
        <ThemeToggle />
      </div>
    </div>
  );
}
