/* ----------  Light theme (default) ---------- */
@import "tailwindcss";
@import "tailwindcss-animated";
@plugin "daisyui" {
  themes: false;
}

@plugin "daisyui/theme" {
  name: "nonail-light";
  default: true;
  prefersdark: false;
  color-scheme: light;

  /* Base */
  --color-base-100: oklch(98% 0.02 280);
  --color-base-200: oklch(96% 0.03 280);
  --color-base-300: oklch(94% 0.04 280);
  --color-base-content: oklch(20% 0.05 280);

  /* Primary */
  --color-primary: oklch(89% 0.09 300);
  --color-primary-content: oklch(25% 0.05 300);

  /* Secondary */
  --color-secondary: oklch(91% 0.07 30);
  --color-secondary-content: oklch(25% 0.05 30);

  /* Accent */
  --color-accent: oklch(78% 0.07 290);
  --color-accent-content: oklch(25% 0.02 290);

  /* Neutral */
  --color-neutral: oklch(50% 0.05 280);
  --color-neutral-content: oklch(98% 0.01 280);

  /* Semantic */
  --color-info: oklch(78% 0.05 290);
  --color-info-content: oklch(25% 0.02 290);
  --color-success: oklch(80% 0.06 150);
  --color-success-content: oklch(25% 0.02 150);
  --color-warning: oklch(85% 0.06 45);
  --color-warning-content: oklch(25% 0.02 45);
  --color-error: oklch(75% 0.07 10);
  --color-error-content: oklch(98% 0.01 10);

  /* Border-radius & sizes */
  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

/* ----------  Dark theme ---------- */
@plugin "daisyui/theme" {
  name: "nonail-dark";
  default: false;
  prefersdark: true;
  color-scheme: dark;

  /* Base (ลด Lightness ~10-12 จุด) */
  --color-base-100: oklch(16% 0.02 280);
  --color-base-200: oklch(14% 0.03 280);
  --color-base-300: oklch(12% 0.04 280);
  --color-base-content: oklch(85% 0.02 280);

  /* Primary/Secondary สีเดิมแต่ลด Lightness */
  --color-primary: oklch(75% 0.09 300);
  --color-primary-content: oklch(15% 0.05 300);

  --color-secondary: oklch(77% 0.07 30);
  --color-secondary-content: oklch(15% 0.05 30);

  /* Accent */
  --color-accent: oklch(65% 0.07 290);
  --color-accent-content: oklch(15% 0.02 290);

  /* Neutral */
  --color-neutral: oklch(35% 0.05 280);
  --color-neutral-content: oklch(85% 0.01 280);

  /* Semantic (ลด Lightness ประมาณ 15-20 จุด) */
  --color-info: oklch(65% 0.05 290);
  --color-info-content: oklch(15% 0.02 290);
  --color-success: oklch(67% 0.06 150);
  --color-success-content: oklch(15% 0.02 150);
  --color-warning: oklch(72% 0.06 45);
  --color-warning-content: oklch(15% 0.02 45);
  --color-error: oklch(62% 0.07 10);
  --color-error-content: oklch(85% 0.01 10);

  /* Border-radius & sizes ใช้ค่าเดิม */
  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

html,
body,
#root {
  height: 100%;
}

.btn {
  border-radius: 1rem;
}
